/**
 * Módulo de navegación para iPRA
 * Implementa la navegación entre pantallas utilizando delegación de eventos
 */
function setupNavigation() {
    // Manejar el botón de menú principal en el dashboard
    on(document, 'click', '#main-menu-btn,#entity-back-menu-btn', function () {
        // Navegar al menú principal
        navigateTo('main-menu');
    });

    // Añadir la función "back" a app.acciones para volver a la pantalla anterior
    app.acciones.back = function () {
        console.log('Acción back: Volviendo a la pantalla anterior');

        // Obtener el hash actual
        const currentHash = window.location.hash.substring(1);

        // Si estamos en un tablero, determinar a dónde volver
        if (currentHash === 'tablero') {
            const previousHash = sessionStorage.getItem('previousHash');

            if (previousHash && (previousHash.startsWith('usuarios') || previousHash.startsWith('tableros'))) {
                console.log(`Acción back: Actualizando hash guardado: ${previousHash}`);
                // Limpiar el hash guardado
                sessionStorage.removeItem('previousHash');

                // Determinar el tipo de entidad según el hash guardado
                if (previousHash.startsWith('usuarios')) {
                    updateUrlHash('entity-management', { entityType: 'usuarios' });
                } else if (previousHash.startsWith('tableros')) {
                    updateUrlHash('entity-management', { entityType: 'tableros' });
                }
            } else {
                console.log('Acción back: Actualizando hash a tableros');
                // Actualizar el hash a tableros por defecto
                updateUrlHash('entity-management', { entityType: 'tableros' });
            }
        }

        // Volver atrás en la historia del navegador
        // Esto disparará handlePopState que se encargará de la navegación
        window.history.back();
    };

    // Ir al menú principal
    app.acciones.inicio = function () {
        navigateTo('main-menu');
    }
}